#!/usr/bin/env python3
"""
Test script for the Flow Configuration System

This script demonstrates how the flow configuration system works
and validates that all components are functioning correctly.
"""

import sys
from pathlib import Path

# Add the flows directory to the path
flows_dir = Path(__file__).parent
sys.path.insert(0, str(flows_dir))

try:
    # Import the modules directly
    import flow_loader
    import input_transformations

    FlowConfigurationLoader = flow_loader.FlowConfigurationLoader
    FlowInputTransformer = input_transformations.FlowInputTransformer
    InputTransformationError = input_transformations.InputTransformationError
    print("✅ Flow system imports successful")
except ImportError as e:
    print(f"❌ Failed to import flow system: {e}")
    sys.exit(1)


def test_flow_loader():
    """Test the flow configuration loader."""
    print("\n=== Testing Flow Configuration Loader ===")
    
    loader = FlowConfigurationLoader()
    
    # Test listing available flows
    available_flows = loader.list_available_flows()
    print(f"Available flows: {available_flows}")
    
    # Test loading a specific flow
    if "schedule_tour" in available_flows:
        flow_config = loader.load_flow_configuration("schedule_tour")
        if flow_config:
            print(f"✅ Loaded schedule_tour flow configuration")
            print(f"   Version: {flow_config.version}")
            print(f"   Description: {flow_config.description}")
        else:
            print("❌ Failed to load schedule_tour flow configuration")
    
    # Test enhanced system prompt
    if "schedule_tour" in available_flows:
        enhanced_prompt = loader.get_enhanced_system_prompt("schedule_tour")
        print(f"✅ Generated enhanced system prompt ({len(enhanced_prompt)} characters)")
    
    return True


def test_input_transformations():
    """Test the input transformation utilities."""
    print("\n=== Testing Input Transformations ===")
    
    transformer = FlowInputTransformer()
    
    # Test datetime transformations
    test_cases = [
        ("datetime", "tomorrow", "Should parse 'tomorrow'"),
        ("datetime", "2 PM", "Should parse '2 PM'"),
        ("datetime", "next Tuesday", "Should parse 'next Tuesday'"),
        ("phone_number", "************", "Should format phone number"),
        ("phone_number", "5551234567", "Should format 10-digit number"),
        ("move_date", "next month", "Should parse 'next month'"),
    ]
    
    for input_type, user_input, description in test_cases:
        try:
            result = transformer.transform_input(input_type, user_input)
            print(f"✅ {description}: '{user_input}' → '{result}'")
        except InputTransformationError as e:
            print(f"⚠️  {description}: '{user_input}' → Error: {e}")
        except Exception as e:
            print(f"❌ {description}: '{user_input}' → Unexpected error: {e}")
    
    return True


def test_error_handling():
    """Test error handling capabilities."""
    print("\n=== Testing Error Handling ===")
    
    loader = FlowConfigurationLoader()
    
    # Test error handling guidance
    error_guidance = loader.get_error_handling_guidance("schedule_tour", "ambiguous_datetime")
    if error_guidance:
        print(f"✅ Error handling guidance for ambiguous_datetime: {error_guidance}")
    else:
        print("⚠️  No error handling guidance found for ambiguous_datetime")
    
    # Test validation
    test_input = {
        "property_id": 123,
        "first_name": "John",
        "last_name": "Smith",
        "email": "<EMAIL>",
        "phone_number": "(*************"
    }
    
    validation_errors = loader.validate_tool_input("schedule_tour", test_input)
    if not validation_errors:
        print("✅ Validation passed for valid input")
    else:
        print(f"⚠️  Validation errors: {validation_errors}")
    
    return True


def test_integration_example():
    """Test a complete integration example."""
    print("\n=== Testing Complete Integration Example ===")
    
    loader = FlowConfigurationLoader()
    transformer = FlowInputTransformer()
    
    # Simulate a user wanting to schedule a tour
    user_inputs = {
        "property_request": "property 123",
        "time_request": "tomorrow at 2 PM",
        "phone_input": "************",
        "move_date_input": "next month"
    }
    
    print("User inputs (natural language):")
    for key, value in user_inputs.items():
        print(f"  {key}: '{value}'")
    
    # Transform inputs
    transformed_inputs = {}
    try:
        # Extract property ID (simple regex for demo)
        import re
        property_match = re.search(r'property (\d+)', user_inputs["property_request"])
        if property_match:
            transformed_inputs["property_id"] = int(property_match.group(1))
        
        # Transform datetime
        transformed_inputs["tour_time"] = transformer.transform_input("datetime", user_inputs["time_request"])
        
        # Transform phone
        transformed_inputs["phone"] = transformer.transform_input("phone_number", user_inputs["phone_input"])
        
        # Transform move date
        transformed_inputs["move_date"] = transformer.transform_input("move_date", user_inputs["move_date_input"])
        
        print("\nTransformed inputs (technical format):")
        for key, value in transformed_inputs.items():
            print(f"  {key}: {value}")
        
        print("✅ Complete integration example successful!")
        
    except Exception as e:
        print(f"❌ Integration example failed: {e}")
        return False
    
    return True


def main():
    """Run all tests."""
    print("🎯 Flow Configuration System Test Suite")
    print("=" * 50)
    
    tests = [
        test_flow_loader,
        test_input_transformations,
        test_error_handling,
        test_integration_example,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Flow configuration system is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
