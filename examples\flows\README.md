# Flow Configuration System

This directory contains flow configuration files that guide AI agents to handle user input more intelligently, eliminating the need for users to provide perfectly formatted input.

## Overview

The flow configuration system addresses a critical UX issue where AI agents force users to provide perfectly formatted input (like timezone-specific datetime strings and exact phone number formats) instead of intelligently parsing and reformatting user input automatically.

## Problem Statement

Without flow configurations, AI agents exhibit poor UX patterns like:
- Asking users to reformat "8:30 PM" to "20:30:00"
- Requiring phone numbers in exact format "(XXX) XXX-XXXX"
- Demanding ISO datetime strings with timezone information
- Repeatedly asking for the same information due to format issues

## Solution

Flow configurations teach AI agents to:
- Parse natural language dates/times and convert to required ISO formats
- Standardize phone number formats without asking users to reformat
- Handle timezone inference (default to user's local timezone or ask once)
- Convert casual input to structured data seamlessly

## Flow Configuration Schema

Each flow configuration file follows this YAML structure:

```yaml
# Tool identification
tool_name: "schedule_tour"
version: "1.0"

# AI behavior guidance
role_prompt: |
  Instructions for how the AI should behave and handle user input

task_prompt: |
  Specific guidance for the task at hand

# Input transformation rules
input_transformations:
  datetime:
    - pattern: "natural language time expressions"
      transform: "ISO format conversion logic"
  phone_number:
    - pattern: "various phone formats"
      transform: "standardized format"

# Validation and error handling
validation_rules:
  required_fields: []
  format_requirements: {}

error_handling:
  strategies: []
  fallback_prompts: []

# User experience guidelines
user_experience_guidelines:
  principles: []
  conversation_flow: []
```

## Available Flow Configurations

- `schedule_tour_flow.yaml` - Intelligent tour scheduling with natural input parsing
- `pricing_availability_flow.yaml` - Smart pricing and availability queries
- `property_info_flow.yaml` - Natural property information requests

## Usage

Flow configurations are automatically loaded by AI clients and applied when the corresponding MCP tool is used. The AI agent will follow the guidance in the flow configuration to provide a smooth, intelligent user experience.

## Benefits

1. **Improved User Experience**: Users can provide input naturally without worrying about exact formats
2. **Reduced Friction**: Eliminates back-and-forth formatting requests
3. **Intelligent Parsing**: AI agents automatically convert user input to required formats
4. **Consistent Behavior**: Standardized approach across all tools
5. **Maintainable**: Easy to update and extend flow configurations
